"use client";

import { useAuth } from "@/lib/auth-context";
import { getUserRole, hasPermission } from "@/lib/permissions";
import {
	Bell,
	Calendar,
	LayoutDashboard,
	Settings,
	UserCheck,
	UserCircle,
	Users,
	Wrench
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const AdminSidebar = () => {
  const pathname = usePathname();
  const { user } = useAuth();
  const userRole = getUserRole(user);

  if (pathname === '/admin/login') return null;

  const navigation = [
    { name: 'Tableau de bord', href: '/admin/dashboard', icon: LayoutDashboard, permission: 'reservations:read' },
    { name: 'Services', href: '/admin/services', icon: Wrench, permission: 'services:read' },
    { name: 'Réservations', href: '/admin/reservations', icon: Calendar, permission: 'reservations:read' },
    { name: 'Planning', href: '/admin/schedule', icon: User<PERSON>heck, permission: 'reservations:read' },
    { name: 'Clients', href: '/admin/clients', icon: UserCircle, permission: 'customers:read' },
    { name: 'Utilisateurs', href: '/admin/users', icon: Users, permission: 'users:read' },
    { name: 'Notifications', href: '/admin/notifications', icon: Bell, permission: 'reservations:read' },
    { name: 'Paramètres', href: '/admin/settings', icon: Settings, permission: 'settings:read' }
  ];

  // Filter navigation items based on user permissions
  const filteredNavigation = navigation.filter(item =>
    !item.permission || hasPermission(userRole, item.permission as any)
  );

  return (
    <div className="bg-gray-900 text-white w-64 min-h-screen">
      <nav className="mt-8">
        <div className="px-4">
          <ul className="space-y-2">
            {filteredNavigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;

              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      isActive
                        ? 'bg-emerald-600 text-white'
                        : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </nav>
    </div>
  );
};

export default AdminSidebar;
