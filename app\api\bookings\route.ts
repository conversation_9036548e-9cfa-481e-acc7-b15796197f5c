import { reserveEquipment } from '@/lib/availability'
import { calculateBookingPrice, validateBookingForCreation } from '@/lib/booking-validation'
import { supabase } from '@/lib/supabase'
import { BookingFormData } from '@/lib/types'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const bookingData: BookingFormData = await request.json()

    // Validate booking data
    const validation = await validateBookingForCreation(bookingData)
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validation.errors,
          warnings: validation.warnings 
        },
        { status: 400 }
      )
    }

    // Calculate final pricing
    const priceCalculation = await calculateBookingPrice(
      bookingData.serviceId,
      bookingData.participants
    )

    // Start transaction
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .upsert({
        email: bookingData.customerInfo.email,
        first_name: bookingData.customerInfo.firstName,
        last_name: bookingData.customerInfo.lastName,
        phone: bookingData.customerInfo.phone,
        emergency_contact_name: bookingData.customerInfo.emergencyContactName,
        emergency_contact_phone: bookingData.customerInfo.emergencyContactPhone
      }, {
        onConflict: 'email'
      })
      .select()
      .single()

    if (customerError) {
      console.error('Error creating/updating customer:', customerError)
      return NextResponse.json(
        { error: 'Failed to create customer record' },
        { status: 500 }
      )
    }

    // Create reservation
    const reservationData = {
      service_id: bookingData.serviceId,
      time_slot_id: bookingData.timeSlotId,
      customer_id: customer.id,
      total_participants: bookingData.participants.length,
      total_amount: priceCalculation.total,
      status: 'pending' as const,
      special_requests: bookingData.specialRequests || null,
      qr_code: generateQRCode() // Simple QR code generation
    }

    const { data: reservation, error: reservationError } = await supabase
      .from('reservations')
      .insert(reservationData)
      .select()
      .single()

    if (reservationError) {
      console.error('Error creating reservation:', reservationError)
      return NextResponse.json(
        { error: 'Failed to create reservation' },
        { status: 500 }
      )
    }

    // Create participant records
    const participantRecords = bookingData.participants.map((participant, index) => {
      const participantPricing = priceCalculation.participants[index]
      return {
        reservation_id: reservation.id,
        first_name: participant.firstName,
        last_name: participant.lastName,
        age: participant.age,
        pricing_tier_id: participantPricing.tier.id,
        individual_price: participantPricing.price,
        dietary_restrictions: participant.dietaryRestrictions || null,
        medical_conditions: participant.medicalConditions || null
      }
    })

    const { error: participantsError } = await supabase
      .from('reservation_participants')
      .insert(participantRecords)

    if (participantsError) {
      console.error('Error creating participants:', participantsError)
      // Rollback reservation
      await supabase.from('reservations').delete().eq('id', reservation.id)
      return NextResponse.json(
        { error: 'Failed to create participant records' },
        { status: 500 }
      )
    }

    // Reserve equipment
    const equipmentReserved = await reserveEquipment(
      bookingData.serviceId,
      bookingData.timeSlotId,
      bookingData.participants.length,
      reservation.id
    )

    if (!equipmentReserved) {
      console.error('Failed to reserve equipment')
      // Rollback reservation and participants
      await supabase.from('reservation_participants').delete().eq('reservation_id', reservation.id)
      await supabase.from('reservations').delete().eq('id', reservation.id)
      return NextResponse.json(
        { error: 'Failed to reserve equipment' },
        { status: 500 }
      )
    }

    // Update time slot status if fully booked
    const { data: timeSlot } = await supabase
      .from('time_slots')
      .select('*')
      .eq('id', bookingData.timeSlotId)
      .single()

    if (timeSlot) {
      // Check if time slot is now fully booked
      const { data: totalReservations } = await supabase
        .from('reservations')
        .select('total_participants')
        .eq('time_slot_id', bookingData.timeSlotId)
        .neq('status', 'cancelled')

      const totalParticipants = totalReservations?.reduce(
        (sum, res) => sum + res.total_participants, 0
      ) || 0

      // Get service max participants
      const { data: service } = await supabase
        .from('services')
        .select('max_participants')
        .eq('id', bookingData.serviceId)
        .single()

      if (service && totalParticipants >= service.max_participants) {
        await supabase
          .from('time_slots')
          .update({ status: 'booked' })
          .eq('id', bookingData.timeSlotId)
      }
    }

    // Create notification for admin about new booking
    try {
      const { createNotification, createBookingConfirmationNotification } = await import('@/lib/notifications')

      // Get service and customer details for notification
      const { data: service } = await supabase
        .from('services')
        .select('name')
        .eq('id', bookingData.serviceId)
        .single()

      const { data: timeSlot } = await supabase
        .from('time_slots')
        .select('start_time')
        .eq('id', bookingData.timeSlotId)
        .single()

      if (service && timeSlot) {
        const customerName = `${customer.first_name} ${customer.last_name}`
        const serviceName = service.name
        const date = new Date(timeSlot.start_time).toLocaleDateString('fr-FR')

        // Get admin users to notify
        const { data: adminProfiles } = await supabase
          .from('profiles')
          .select('id')
          .eq('role', 'admin')

        // Create notification for each admin
        if (adminProfiles) {
          const notificationTemplate = createBookingConfirmationNotification(
            customerName,
            serviceName,
            date,
            reservation.id
          )

          for (const admin of adminProfiles) {
            await createNotification(admin.id, notificationTemplate, reservation.id)
          }
        }
      }
    } catch (notificationError) {
      console.error('Error creating booking notification:', notificationError)
      // Don't fail the booking if notification fails
    }

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        reservationId: reservation.id,
        qrCode: reservation.qr_code,
        totalAmount: priceCalculation.total,
        status: 'pending',
        message: 'Réservation créée avec succès'
      },
      warnings: validation.warnings
    })

  } catch (error) {
    console.error('Error creating booking:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customer_id')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '10')

    let query = supabase
      .from('reservations')
      .select(`
        *,
        service:services(name, duration_minutes),
        time_slot:time_slots(start_time, end_time),
        customer:customers(first_name, last_name, email),
        participants:reservation_participants(*)
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (customerId) {
      query = query.eq('customer_id', customerId)
    }

    if (status) {
      query = query.eq('status', status)
    }

    const { data: reservations, error } = await query

    if (error) {
      console.error('Error fetching reservations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch reservations' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: reservations
    })

  } catch (error) {
    console.error('Error in GET /api/bookings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Utility function to generate QR code
function generateQRCode(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `RES-${timestamp}-${random}`.toUpperCase()
}
