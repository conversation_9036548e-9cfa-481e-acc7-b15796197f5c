"use client";

import { useAuth } from "@/lib/auth-context";
import { getUserRole, hasPermission } from "@/lib/permissions";
import { Bell, User } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

const AdminHeader = () => {
	const pathname = usePathname();
	const router = useRouter();
	const { user, signOut } = useAuth();
	const [showUserMenu, setShowUserMenu] = useState(false);
	const userRole = getUserRole(user);

	const handleLogout = async () => {
		try {
			await signOut();
			router.push("/admin/login");
		} catch (error) {
			console.error("Logout error:", error);
			// Force redirect even if logout fails
			router.push("/admin/login");
		}
	};

	if (pathname === "/admin/login") return null;

	return (
		<header className="bg-white shadow-sm border-b border-gray-200">
			<div className="w-full px-2">
				<div className="flex justify-between items-center h-16">
					<Link href="/admin/dashboard" className="flex items-center space-x-3 flex-shrink-0">
						<img src="/images/logo-hd.png" alt="Soleil & Découverte" className="h-10 w-auto" />
						<span className="text-lg font-semibold text-gray-900">Administration</span>
					</Link>

					<div className="flex items-center space-x-4 flex-shrink-0">
						<Link href="/admin/notifications" className="p-2 text-gray-400 hover:text-gray-600 relative">
							<Bell className="h-5 w-5" />
							<span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center text-xs text-white">
								3
							</span>
						</Link>

						<div className="relative">
							<button
								onClick={() => setShowUserMenu(!showUserMenu)}
								className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50"
							>
								<div className="h-8 w-8 bg-emerald-100 rounded-full flex items-center justify-center">
									<User className="h-4 w-4 text-emerald-600" />
								</div>
								<div className="text-left">
									<div className="text-sm font-medium text-gray-700">
										{user?.user_metadata?.first_name && user?.user_metadata?.last_name
											? `${user.user_metadata.first_name} ${user.user_metadata.last_name}`
											: user?.email?.split("@")[0] || "Admin"}
									</div>
									<div className="text-xs text-gray-500 capitalize">
										{user?.user_metadata?.role || "admin"}
									</div>
								</div>
							</button>

							{showUserMenu && (
								<div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
									<div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
										<div className="font-medium">{user?.email}</div>
										<div className="text-xs text-gray-500 capitalize">
											Rôle: {user?.user_metadata?.role}
										</div>
									</div>
									<Link
										href="/admin/profile"
										className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
										onClick={() => setShowUserMenu(false)}
									>
										Mon profil
									</Link>
									{hasPermission(userRole, 'settings:read') && (
										<Link
											href="/admin/settings"
											className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
											onClick={() => setShowUserMenu(false)}
										>
											Paramètres
										</Link>
									)}
									<button
										onClick={() => {
											setShowUserMenu(false);
											handleLogout();
										}}
										className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
									>
										Se déconnecter
									</button>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</header>
	);
};

export default AdminHeader;
