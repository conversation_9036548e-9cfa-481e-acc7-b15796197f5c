"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, CheckC<PERSON>cle, Clock, MessageSquare, Trash2 } from "lucide-react";
import { useState } from "react";
import Button from "./ui/Button";

const AdminNotifications = () => {
	const [notifications, setNotifications] = useState<UINotification[]>([]);
	const [filter, setFilter] = useState<string>("all");
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Fetch notifications from API
	const fetchNotifications = async () => {
		try {
			setLoading(true);
			setError(null);

			const response = await fetch('/api/admin/notifications');
			if (!response.ok) {
				throw new Error('Failed to fetch notifications');
			}

			const result = await response.json();
			if (result.success) {
				// Map database notifications to UI format
				const uiNotifications: UINotification[] = result.data.map((dbNotif: any) => ({
					id: dbNotif.id,
					type: mapDbTypeToUI(dbNotif.notification_type),
					title: dbNotif.subject,
					message: dbNotif.content,
					timestamp: dbNotif.created_at,
					read: dbNotif.is_read || false,
					priority: dbNotif.priority || 'medium',
					reservation_id: dbNotif.reservation_id
				}));
				setNotifications(uiNotifications);
			} else {
				throw new Error(result.error || 'Failed to fetch notifications');
			}
		} catch (err) {
			console.error('Error fetching notifications:', err);
			setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
			toast.error('Failed to load notifications');
		} finally {
			setLoading(false);
		}
	};

	// Map database notification types to UI types
	const mapDbTypeToUI = (dbType: string): UINotification['type'] => {
		const mapping: Record<string, UINotification['type']> = {
			'booking_confirmation': 'booking',
			'reminder': 'booking',
			'cancellation': 'booking',
			'refund': 'payment',
			'feedback_request': 'message',
			'promotional': 'message',
			'booking': 'booking',
			'payment': 'payment',
			'system': 'system',
			'message': 'message',
			'weather': 'weather'
		};
		return mapping[dbType] || 'system';
	};

	// Load notifications on component mount
	useEffect(() => {
		fetchNotifications();
	}, []);

	const getTypeIcon = (type: string) => {
		switch (type) {
			case "booking":
				return <Bell className="h-5 w-5 text-blue-600" />;
			case "payment":
				return <CheckCircle className="h-5 w-5 text-green-600" />;
			case "system":
				return <AlertCircle className="h-5 w-5 text-orange-600" />;
			case "message":
				return <MessageSquare className="h-5 w-5 text-purple-600" />;
			default:
				return <Bell className="h-5 w-5 text-gray-600" />;
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case "high":
				return "bg-red-100 text-red-800";
			case "medium":
				return "bg-yellow-100 text-yellow-800";
			case "low":
				return "bg-green-100 text-green-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getPriorityText = (priority: string) => {
		switch (priority) {
			case "high":
				return "Haute";
			case "medium":
				return "Moyenne";
			case "low":
				return "Basse";
			default:
				return priority;
		}
	};

	const markAsRead = async (id: string) => {
		try {
			const response = await fetch(`/api/admin/notifications/${id}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ is_read: true })
			});

			if (!response.ok) {
				throw new Error('Failed to mark notification as read');
			}

			// Update local state
			setNotifications(notifications.map((notif) =>
				notif.id === id ? { ...notif, read: true } : notif
			));
			toast.success('Notification marked as read');
		} catch (err) {
			console.error('Error marking notification as read:', err);
			toast.error('Failed to mark notification as read');
		}
	};

	const deleteNotification = async (id: string) => {
		try {
			const response = await fetch(`/api/admin/notifications/${id}`, {
				method: 'DELETE'
			});

			if (!response.ok) {
				throw new Error('Failed to delete notification');
			}

			// Update local state
			setNotifications(notifications.filter((notif) => notif.id !== id));
			toast.success('Notification deleted');
		} catch (err) {
			console.error('Error deleting notification:', err);
			toast.error('Failed to delete notification');
		}
	};

	const markAllAsRead = async () => {
		try {
			const unreadIds = notifications.filter(n => !n.read).map(n => n.id);
			if (unreadIds.length === 0) return;

			const response = await fetch('/api/admin/notifications', {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					action: 'mark_read',
					notification_ids: unreadIds
				})
			});

			if (!response.ok) {
				throw new Error('Failed to mark all notifications as read');
			}

			// Update local state
			setNotifications(notifications.map((notif) => ({ ...notif, read: true })));
			toast.success('All notifications marked as read');
		} catch (err) {
			console.error('Error marking all notifications as read:', err);
			toast.error('Failed to mark all notifications as read');
		}
	};

	const filteredNotifications = notifications.filter((notif) => {
		if (filter === "all") return true;
		if (filter === "unread") return !notif.read;
		return notif.type === filter;
	});

	const unreadCount = notifications.filter((notif) => !notif.read).length;

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
					<span className="ml-2 text-gray-600">Chargement des notifications...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="p-6">
				<div className="bg-red-50 border border-red-200 rounded-lg p-4">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-800">Erreur: {error}</span>
					</div>
					<Button
						onClick={fetchNotifications}
						className="mt-3 bg-red-600 hover:bg-red-700"
					>
						Réessayer
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						Notifications
						{unreadCount > 0 && (
							<span className="ml-2 bg-red-500 text-white text-sm px-2 py-1 rounded-full">
								{unreadCount}
							</span>
						)}
					</h1>
					<p className="text-gray-600">Gérez vos notifications et alertes</p>
				</div>
				<div className="flex gap-2">
					<Button
						onClick={fetchNotifications}
						variant="outline"
						disabled={loading}
					>
						{loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Actualiser'}
					</Button>
					<Button onClick={markAllAsRead} disabled={unreadCount === 0}>
						Tout marquer comme lu
					</Button>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
				<div className="flex flex-wrap gap-2">
					<button
						onClick={() => setFilter("all")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "all"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Toutes ({notifications.length})
					</button>
					<button
						onClick={() => setFilter("unread")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "unread"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Non lues ({unreadCount})
					</button>
					<button
						onClick={() => setFilter("booking")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "booking"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Réservations
					</button>
					<button
						onClick={() => setFilter("payment")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "payment"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Paiements
					</button>
					<button
						onClick={() => setFilter("system")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "system"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Système
					</button>
					<button
						onClick={() => setFilter("message")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "message"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Messages
					</button>
				</div>
			</div>

			{/* Notifications List */}
			<div className="space-y-4">
				{filteredNotifications.length === 0 ? (
					<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
						<Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							{filter === 'unread' ? 'Aucune notification non lue' :
							 filter === 'all' ? 'Aucune notification' :
							 `Aucune notification de type "${filter}"`}
						</h3>
						<p className="text-gray-600">
							{filter === 'unread' ? 'Toutes vos notifications ont été lues.' :
							 'Les nouvelles notifications apparaîtront ici.'}
						</p>
					</div>
				) : (
					filteredNotifications.map((notification) => (
					<div
						key={notification.id}
						className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${
							!notification.read ? "border-l-4 border-l-emerald-500" : ""
						}`}
					>
						<div className="flex items-start justify-between">
							<div className="flex items-start gap-4 flex-1">
								<div className="bg-gray-100 p-2 rounded-lg">{getTypeIcon(notification.type)}</div>
								<div className="flex-1">
									<div className="flex items-center gap-2 mb-1">
										<h3
											className={`text-lg font-medium ${
												!notification.read ? "text-gray-900" : "text-gray-700"
											}`}
										>
											{notification.title}
										</h3>
										<span
											className={`px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(
												notification.priority
											)}`}
										>
											{getPriorityText(notification.priority)}
										</span>
										{!notification.read && (
											<span className="w-2 h-2 bg-emerald-500 rounded-full"></span>
										)}
									</div>
									<p className="text-gray-600 mb-2">{notification.message}</p>
									<div className="flex items-center gap-2 text-sm text-gray-500">
										<Clock className="h-4 w-4" />
										{new Date(notification.timestamp).toLocaleString("fr-FR")}
									</div>
								</div>
							</div>
							<div className="flex gap-2">
								{!notification.read && (
									<button
										onClick={() => markAsRead(notification.id)}
										className="p-2 text-gray-400 hover:text-emerald-600"
										title="Marquer comme lu"
									>
										<CheckCircle className="h-4 w-4" />
									</button>
								)}
								<button
									onClick={() => deleteNotification(notification.id)}
									className="p-2 text-gray-400 hover:text-red-600"
									title="Supprimer"
								>
									<Trash2 className="h-4 w-4" />
								</button>
							</div>
						</div>
					</div>
				))}
			</div>


		</div>
	);
};

export default AdminNotifications;
